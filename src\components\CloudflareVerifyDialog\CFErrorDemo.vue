<template>
  <div class="cf-error-demo">
    <h3>CF Verification Error Handling Demo</h3>
    
    <!-- 错误信息显示 -->
    <div v-if="errorInfo" class="error-display">
      <div class="error-header">
        <h4>{{ errorInfo.title }}</h4>
        <span class="error-code">Error Code: {{ errorInfo.code }}</span>
        <span class="severity" :class="errorInfo.severity">{{ errorInfo.severity.toUpperCase() }}</span>
      </div>
      
      <div class="error-message">
        {{ errorInfo.message }}
      </div>
      
      <div v-if="errorInfo.suggestions.length > 0" class="suggestions">
        <h5>Suggested Solutions:</h5>
        <ul>
          <li v-for="suggestion in errorInfo.suggestions" :key="suggestion">
            {{ suggestion }}
          </li>
        </ul>
      </div>
      
      <div class="error-actions">
        <button 
          v-if="errorInfo.retryable" 
          @click="handleRetry"
          class="retry-btn"
        >
          Retry Verification
        </button>
        <button @click="clearError" class="clear-btn">
          Clear Error
        </button>
      </div>
    </div>
    
    <!-- 测试按钮 -->
    <div class="test-controls">
      <h4>Test Different CF Errors:</h4>
      <button @click="simulateError('600010')">Simulate 600010 Error</button>
      <button @click="simulateError('110500')">Simulate 110500 Error</button>
      <button @click="simulateError('200010')">Simulate 200010 Error</button>
      <button @click="simulateError('200100')">Simulate 200100 Error</button>
      <button @click="simulateError('300001')">Simulate 300001 Error</button>
    </div>
    
    <!-- 验证组件 -->
    <div class="verification-section">
      <h4>CF Verification:</h4>
      <button @click="startVerification" :disabled="isVerifying">
        {{ isVerifying ? 'Verifying...' : 'Start Verification' }}
      </button>
      
      <CloudflareVerifyDialog
        v-model="showDialog"
        :cf-type="CF_TURNSTILE_TYPE.LOGIN_SUBMIT"
        title="Test Verification"
        description="This is a test verification dialog"
        @success="handleSuccess"
        @error="handleError"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CFErrorHelper, type CFErrorInfo } from '@/utils/CFErrorHelper';
import { CF_TURNSTILE_TYPE } from '@/utils/CloudflareMgr';
import CloudflareVerifyDialog from './index.vue';

// 响应式数据
const errorInfo = ref<CFErrorInfo | null>(null);
const showDialog = ref(false);
const isVerifying = ref(false);

/**
 * 模拟不同的 CF 错误
 */
const simulateError = (errorCode: string) => {
  const simulatedError = `${errorCode}: Simulated error for testing`;
  const info = CFErrorHelper.getErrorInfo(simulatedError);
  errorInfo.value = info;
  
  // 记录错误信息
  CFErrorHelper.logError(info, { simulated: true, errorCode });
};

/**
 * 处理重试
 */
const handleRetry = () => {
  console.log('🔄 Retrying verification...');
  clearError();
  startVerification();
};

/**
 * 清除错误
 */
const clearError = () => {
  errorInfo.value = null;
};

/**
 * 开始验证
 */
const startVerification = () => {
  isVerifying.value = true;
  showDialog.value = true;
};

/**
 * 处理验证成功
 */
const handleSuccess = (result: any) => {
  console.log('✅ Verification successful:', result);
  isVerifying.value = false;
  showDialog.value = false;
  clearError();
};

/**
 * 处理验证错误
 */
const handleError = (error: string) => {
  console.error('❌ Verification failed:', error);
  isVerifying.value = false;
  showDialog.value = false;
  
  // 解析错误信息
  const info = CFErrorHelper.getErrorInfo(error);
  errorInfo.value = info;
};

/**
 * 处理验证取消
 */
const handleCancel = () => {
  console.log('❌ Verification cancelled');
  isVerifying.value = false;
  showDialog.value = false;
};
</script>

<style scoped>
.cf-error-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.error-display {
  border: 1px solid #e74c3c;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background-color: #fdf2f2;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.error-header h4 {
  margin: 0;
  color: #c0392b;
}

.error-code {
  background-color: #e74c3c;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.severity {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.severity.low {
  background-color: #f39c12;
  color: white;
}

.severity.medium {
  background-color: #e67e22;
  color: white;
}

.severity.high {
  background-color: #e74c3c;
  color: white;
}

.error-message {
  margin-bottom: 15px;
  line-height: 1.5;
  color: #2c3e50;
}

.suggestions {
  margin-bottom: 20px;
}

.suggestions h5 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.suggestions li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.error-actions {
  display: flex;
  gap: 10px;
}

.retry-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background-color: #2980b9;
}

.clear-btn {
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.clear-btn:hover {
  background-color: #7f8c8d;
}

.test-controls {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #bdc3c7;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.test-controls h4 {
  margin-top: 0;
}

.test-controls button {
  margin: 5px;
  padding: 8px 12px;
  background-color: #34495e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-controls button:hover {
  background-color: #2c3e50;
}

.verification-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #27ae60;
  border-radius: 8px;
  background-color: #f0fff4;
}

.verification-section h4 {
  margin-top: 0;
  color: #27ae60;
}

.verification-section button {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.verification-section button:hover:not(:disabled) {
  background-color: #229954;
}

.verification-section button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}
</style>
