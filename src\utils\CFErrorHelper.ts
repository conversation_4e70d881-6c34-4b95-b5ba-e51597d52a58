/**
 * CF 验证错误处理助手
 * 提供用户友好的错误信息和解决建议
 */

import { CFErrorHandler } from './CloudflareMgr';

export interface CFErrorInfo {
  code: string;
  title: string;
  message: string;
  suggestions: string[];
  retryable: boolean;
  severity: 'low' | 'medium' | 'high';
}

/**
 * CF 错误助手类
 */
export class CFErrorHelper {
  /**
   * 获取用户友好的错误信息
   */
  static getErrorInfo(error: string | { error?: string; errorCode?: string }): CFErrorInfo {
    let errorCode = '';
    let errorMessage = '';

    if (typeof error === 'string') {
      const parsed = CFErrorHandler.parseError(error);
      errorCode = parsed.code;
      errorMessage = parsed.message;
    } else if (error && typeof error === 'object') {
      errorCode = error.errorCode || '';
      errorMessage = error.error || 'Unknown error';
    }

    return this.getDetailedErrorInfo(errorCode, errorMessage);
  }

  /**
   * 获取详细的错误信息
   */
  private static getDetailedErrorInfo(errorCode: string, errorMessage: string): CFErrorInfo {
    switch (errorCode) {
      case '600010':
        return {
          code: errorCode,
          title: 'Verification Challenge Failed',
          message: 'The security verification could not be completed. This usually happens when the system detects unusual behavior or potential automation.',
          suggestions: [
            'Clear your browser cache and cookies',
            'Disable browser extensions (especially ad blockers)',
            'Try using a different browser or incognito mode',
            'Check your internet connection stability',
            'Disable VPN or proxy if you\'re using one',
            'Make sure JavaScript is enabled',
            'Try again after a few minutes'
          ],
          retryable: true,
          severity: 'medium'
        };

      case '110500':
        return {
          code: errorCode,
          title: 'Unsupported Browser',
          message: 'Your browser is not supported or is outdated.',
          suggestions: [
            'Update your browser to the latest version',
            'Use a supported browser (Chrome, Firefox, Safari, Edge)',
            'Enable JavaScript if it\'s disabled'
          ],
          retryable: false,
          severity: 'high'
        };

      case '200010':
        return {
          code: errorCode,
          title: 'Caching Issue',
          message: 'Some verification components were cached incorrectly.',
          suggestions: [
            'Clear your browser cache and cookies',
            'Refresh the page and try again',
            'Try using incognito/private browsing mode'
          ],
          retryable: true,
          severity: 'low'
        };

      case '200100':
        return {
          code: errorCode,
          title: 'Time Synchronization Issue',
          message: 'Your system clock appears to be incorrect.',
          suggestions: [
            'Check that your system date and time are correct',
            'Ensure your timezone is set properly',
            'Sync your system clock with internet time servers'
          ],
          retryable: true,
          severity: 'medium'
        };

      case '200500':
        return {
          code: errorCode,
          title: 'Loading Error',
          message: 'The verification component could not be loaded properly.',
          suggestions: [
            'Check your internet connection',
            'Disable browser extensions that might block content',
            'Check if your firewall or antivirus is blocking the verification',
            'Try refreshing the page'
          ],
          retryable: true,
          severity: 'medium'
        };

      default:
        // Handle error code families
        if (errorCode.startsWith('600')) {
          return {
            code: errorCode,
            title: 'Verification Failed',
            message: 'The security verification could not be completed.',
            suggestions: [
              'Try again in a few moments',
              'Clear browser cache and cookies',
              'Disable browser extensions temporarily',
              'Try using a different browser'
            ],
            retryable: true,
            severity: 'medium'
          };
        } else if (errorCode.startsWith('300')) {
          return {
            code: errorCode,
            title: 'Client Error',
            message: 'An error occurred during verification processing.',
            suggestions: [
              'Refresh the page and try again',
              'Clear browser cache',
              'Try using a different browser'
            ],
            retryable: true,
            severity: 'medium'
          };
        } else if (errorCode.startsWith('110')) {
          return {
            code: errorCode,
            title: 'Configuration Error',
            message: 'There is a configuration issue with the verification service.',
            suggestions: [
              'Contact support for assistance',
              'Try again later'
            ],
            retryable: false,
            severity: 'high'
          };
        } else {
          return {
            code: errorCode || 'UNKNOWN',
            title: 'Verification Error',
            message: errorMessage || 'An unknown error occurred during verification.',
            suggestions: [
              'Try refreshing the page',
              'Clear browser cache and cookies',
              'Try using a different browser',
              'Contact support if the problem persists'
            ],
            retryable: true,
            severity: 'medium'
          };
        }
    }
  }

  /**
   * 生成用户友好的错误消息
   */
  static generateUserMessage(errorInfo: CFErrorInfo): string {
    const retryText = errorInfo.retryable ? ' Please try again.' : ' Please contact support.';
    return `${errorInfo.message}${retryText}`;
  }

  /**
   * 检查错误是否应该自动重试
   */
  static shouldAutoRetry(errorCode: string): boolean {
    // 某些错误码可以自动重试
    const autoRetryableCodes = ['200010', '200500'];
    return autoRetryableCodes.includes(errorCode);
  }

  /**
   * 获取重试延迟时间（毫秒）
   */
  static getRetryDelay(errorCode: string, attemptCount: number): number {
    // 基础延迟时间
    const baseDelay = 1000;
    
    // 根据错误类型调整延迟
    let multiplier = 1;
    if (errorCode.startsWith('600')) {
      multiplier = 2; // 600系列错误延迟更长
    } else if (errorCode.startsWith('200')) {
      multiplier = 1; // 200系列错误延迟较短
    }
    
    // 指数退避，但有上限
    const delay = Math.min(baseDelay * multiplier * Math.pow(1.5, attemptCount - 1), 10000);
    return delay;
  }

  /**
   * 记录错误用于分析
   */
  static logError(errorInfo: CFErrorInfo, context?: any): void {
    console.group(`🔍 CF Error Analysis: ${errorInfo.code}`);
    console.log('Title:', errorInfo.title);
    console.log('Message:', errorInfo.message);
    console.log('Retryable:', errorInfo.retryable);
    console.log('Severity:', errorInfo.severity);
    console.log('Suggestions:', errorInfo.suggestions);
    if (context) {
      console.log('Context:', context);
    }
    console.groupEnd();
  }
}

/**
 * 便捷函数：处理 CF 验证错误
 */
export function handleCFError(
  error: string | { error?: string; errorCode?: string },
  onRetry?: () => void,
  showToUser: (message: string, suggestions?: string[]) => void = console.error
): boolean {
  const errorInfo = CFErrorHelper.getErrorInfo(error);
  
  // 记录错误用于调试
  CFErrorHelper.logError(errorInfo);
  
  // 显示用户友好的错误信息
  const userMessage = CFErrorHelper.generateUserMessage(errorInfo);
  showToUser(userMessage, errorInfo.suggestions);
  
  // 如果可以自动重试且提供了重试函数
  if (errorInfo.retryable && onRetry && CFErrorHelper.shouldAutoRetry(errorInfo.code)) {
    const delay = CFErrorHelper.getRetryDelay(errorInfo.code, 1);
    setTimeout(() => {
      console.log(`🔄 Auto-retrying CF verification after ${delay}ms...`);
      onRetry();
    }, delay);
    return true; // 表示将自动重试
  }
  
  return errorInfo.retryable; // 返回是否可以手动重试
}
