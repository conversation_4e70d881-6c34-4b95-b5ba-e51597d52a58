<script setup lang="ts">
import { useKycStore, PHOTO_TYPE } from "@/stores/kyc";
import ZUploadPhoto from "@/components/ZUploadPhoto/index.vue";

const kycStore = useKycStore();

const { simpleFormData, simpleDayInputErrTip } = storeToRefs(kycStore);

const updateStore = (field: keyof typeof simpleFormData.value, value: any) => {
  kycStore.updateSimpleFormData(field, value);
};

const submitDisabled = computed(() => {
  return !(
    simpleFormData.value.first_name &&
    simpleFormData.value.last_name &&
    simpleFormData.value.day &&
    simpleFormData.value.month &&
    simpleFormData.value.year &&
    simpleFormData.value.font_side_url &&
    simpleFormData.value.selfie_picture_url &&
    !simpleDayInputErrTip.value
  );
});
</script>
<template>
  <ZPage :onBack="kycStore.handleSimplePre">
    <div class="content">
      <div class="tip">
        <ZIcon color="#FF936F" type="icon-warn" :size="16"></ZIcon>
        You need to complete the following information before Withdrawal
      </div>
      <div class="form">
        <ZFormField
          label="First Name"
          v-model="simpleFormData.first_name"
          restriction="alphabetic"
          placeholder="Pleasant enter your first name"
          :maxLength="30"
          @input="(value) => updateStore('first_name', value)"
        ></ZFormField>
        <ZFormField
          label="Middle Name"
          v-model="simpleFormData.middle_name"
          :required="false"
          placeholder="Please enter middle name."
          restriction="alphabetic"
          :maxLength="30"
          @input="(value) => updateStore('middle_name', value)"
        ></ZFormField>
        <ZFormField
          label="Last Name"
          v-model="simpleFormData.last_name"
          placeholder="Please enter last name"
          restriction="alphabetic"
          :maxLength="30"
          @input="(value) => updateStore('last_name', value)"
        ></ZFormField>
        <div class="form-item">
          <label>Date of Birth</label>
          <div class="dob-wrapper">
            <input
              v-model="simpleFormData.day"
              placeholder="Day"
              @input="updateStore('day', $event.target.value)"
              type="number"
            />
            <input
              v-model="simpleFormData.month"
              placeholder="Month"
              @input="updateStore('month', $event.target.value)"
              type="number"
            />
            <input
              v-model="simpleFormData.year"
              placeholder="Year"
              @input="updateStore('year', $event.target.value)"
              type="number"
            />
          </div>
          <p class="error" v-if="simpleDayInputErrTip">{{ simpleDayInputErrTip }}</p>
        </div>
        <ZUploadPhoto
          :height="160"
          class="photo-item"
          ref="idPhotoRef"
          label="Government-Issued ID"
          :required="true"
          @confirm="
            (fontSideUrl) => kycStore.updateSimplePhotoBase64(PHOTO_TYPE.UP_ID_PHOTO, fontSideUrl)
          "
          photoLabel="front side of your ID card"
        ></ZUploadPhoto>
        <ZUploadPhoto
          :height="160"
          class="photo-item"
          ref="holdingPhotoRef"
          label="Selfie Holding Valid ID"
          :required="true"
          @confirm="
            (fontSideUrl) =>
              kycStore.updateSimplePhotoBase64(PHOTO_TYPE.UP_ID_PHOTO_HOLDING, fontSideUrl)
          "
          photoLabel="selfie holding valid ID"
        ></ZUploadPhoto>
        <div class="form-item">
          <div class="tips">
            <div class="title">Please upload a photo of your identification card.</div>
            <div class="desc">
              <p>· Please upload a photo of your identification card.</p>
              <p>· Provide a clear, complete photo of the ID.</p>
              <p>· Avoid reflections, shadows, and excessive brightness.</p>
              <p>· Ensure no part of the ID is covered.</p>
              <p>· Place the ID on a flat surface when taking the photo.</p>
              <p>· Use a valid and undamaged ID Only valid IDs from 21+ are accepte</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 提交按钮 -->
      <div class="footer">
        <div class="submit-btn">
          <ZButton type="primary" :click="kycStore.handleSimpleSubmit" :disabled="submitDisabled">
            Confirm
          </ZButton>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
  padding-bottom: 100px;
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.tip {
  color: var(--ff-936-f, #ff936f);
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  background-color: #fffaf8;
  padding: 8px 12px;
  display: flex;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 8px;
  gap: 6px;
  margin-bottom: 10px;
  /* 150% */
}

.footer {
  width: 100%;
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    padding: 20px;
    width: 100%;
  }
}

.photo-container {
  width: 330px;
  height: 160px;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  border-radius: 20px;

  .photo-img {
    position: absolute;
    width: 160px;
    height: 330px;
    transform-origin: 0 0;
    transform: rotate(90deg) translateY(-100%);
    object-fit: fill;
  }
}

.photo-upload {
  display: flex;
  height: 160px;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 20px;
  background: #f4f7fd;
}

.tips {
  .title {
    color: #222;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .desc {
    color: #999;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }
}

.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #ff4757;
      margin-top: 4px;
      font-size: 12px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #333;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #c0c0c0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
