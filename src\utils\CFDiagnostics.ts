/**
 * CF 验证诊断工具
 * 帮助开发者快速诊断和解决 CF 验证问题
 */

import { CFErrorHelper } from './CFErrorHelper';

export interface DiagnosticResult {
  category: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  suggestion?: string;
}

export interface EnvironmentInfo {
  userAgent: string;
  browser: string;
  version: string;
  platform: string;
  language: string;
  cookiesEnabled: boolean;
  javascriptEnabled: boolean;
  localStorageEnabled: boolean;
  sessionStorageEnabled: boolean;
  timezone: string;
  screenResolution: string;
  colorDepth: number;
  pixelRatio: number;
}

/**
 * CF 诊断工具类
 */
export class CFDiagnostics {
  /**
   * 运行完整的诊断检查
   */
  static async runDiagnostics(): Promise<{
    environment: EnvironmentInfo;
    diagnostics: DiagnosticResult[];
    recommendations: string[];
  }> {
    const environment = this.getEnvironmentInfo();
    const diagnostics = await this.runDiagnosticTests(environment);
    const recommendations = this.generateRecommendations(diagnostics);

    return {
      environment,
      diagnostics,
      recommendations
    };
  }

  /**
   * 获取环境信息
   */
  static getEnvironmentInfo(): EnvironmentInfo {
    const ua = navigator.userAgent;
    const browser = this.detectBrowser(ua);
    
    return {
      userAgent: ua,
      browser: browser.name,
      version: browser.version,
      platform: navigator.platform,
      language: navigator.language,
      cookiesEnabled: navigator.cookieEnabled,
      javascriptEnabled: true, // 如果能执行到这里，JS 肯定是启用的
      localStorageEnabled: this.testLocalStorage(),
      sessionStorageEnabled: this.testSessionStorage(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      pixelRatio: window.devicePixelRatio || 1
    };
  }

  /**
   * 运行诊断测试
   */
  static async runDiagnosticTests(env: EnvironmentInfo): Promise<DiagnosticResult[]> {
    const results: DiagnosticResult[] = [];

    // 浏览器兼容性检查
    results.push(this.checkBrowserCompatibility(env));
    
    // JavaScript 功能检查
    results.push(this.checkJavaScriptFeatures());
    
    // 存储功能检查
    results.push(this.checkStorageFeatures(env));
    
    // 网络连接检查
    results.push(await this.checkNetworkConnectivity());
    
    // 时间同步检查
    results.push(await this.checkTimeSync());
    
    // 第三方脚本加载检查
    results.push(await this.checkThirdPartyScripts());

    return results;
  }

  /**
   * 检查浏览器兼容性
   */
  static checkBrowserCompatibility(env: EnvironmentInfo): DiagnosticResult {
    const supportedBrowsers = {
      'Chrome': 60,
      'Firefox': 55,
      'Safari': 12,
      'Edge': 79
    };

    const browserVersion = parseInt(env.version);
    const minVersion = supportedBrowsers[env.browser as keyof typeof supportedBrowsers];

    if (!minVersion) {
      return {
        category: 'Browser Compatibility',
        status: 'warning',
        message: `Browser ${env.browser} may not be fully supported`,
        suggestion: 'Consider using Chrome, Firefox, Safari, or Edge for best compatibility'
      };
    }

    if (browserVersion < minVersion) {
      return {
        category: 'Browser Compatibility',
        status: 'fail',
        message: `Browser version ${env.version} is outdated (minimum: ${minVersion})`,
        suggestion: `Update ${env.browser} to version ${minVersion} or higher`
      };
    }

    return {
      category: 'Browser Compatibility',
      status: 'pass',
      message: `Browser ${env.browser} ${env.version} is supported`
    };
  }

  /**
   * 检查 JavaScript 功能
   */
  static checkJavaScriptFeatures(): DiagnosticResult {
    const requiredFeatures = [
      'Promise',
      'fetch',
      'addEventListener',
      'querySelector',
      'JSON'
    ];

    const missingFeatures = requiredFeatures.filter(feature => {
      return !(feature in window) && !(feature in window.prototype);
    });

    if (missingFeatures.length > 0) {
      return {
        category: 'JavaScript Features',
        status: 'fail',
        message: `Missing required features: ${missingFeatures.join(', ')}`,
        suggestion: 'Update your browser or enable JavaScript'
      };
    }

    return {
      category: 'JavaScript Features',
      status: 'pass',
      message: 'All required JavaScript features are available'
    };
  }

  /**
   * 检查存储功能
   */
  static checkStorageFeatures(env: EnvironmentInfo): DiagnosticResult {
    const issues: string[] = [];

    if (!env.cookiesEnabled) {
      issues.push('Cookies are disabled');
    }

    if (!env.localStorageEnabled) {
      issues.push('Local Storage is not available');
    }

    if (!env.sessionStorageEnabled) {
      issues.push('Session Storage is not available');
    }

    if (issues.length > 0) {
      return {
        category: 'Storage Features',
        status: 'warning',
        message: issues.join(', '),
        suggestion: 'Enable cookies and storage in your browser settings'
      };
    }

    return {
      category: 'Storage Features',
      status: 'pass',
      message: 'All storage features are available'
    };
  }

  /**
   * 检查网络连接
   */
  static async checkNetworkConnectivity(): Promise<DiagnosticResult> {
    try {
      const start = Date.now();
      const response = await fetch('https://challenges.cloudflare.com/cdn-cgi/trace', {
        method: 'GET',
        cache: 'no-cache'
      });
      const duration = Date.now() - start;

      if (!response.ok) {
        return {
          category: 'Network Connectivity',
          status: 'fail',
          message: `Failed to connect to Cloudflare (${response.status})`,
          suggestion: 'Check your internet connection and firewall settings'
        };
      }

      if (duration > 5000) {
        return {
          category: 'Network Connectivity',
          status: 'warning',
          message: `Slow connection to Cloudflare (${duration}ms)`,
          suggestion: 'Check your internet connection speed'
        };
      }

      return {
        category: 'Network Connectivity',
        status: 'pass',
        message: `Connected to Cloudflare successfully (${duration}ms)`
      };
    } catch (error) {
      return {
        category: 'Network Connectivity',
        status: 'fail',
        message: 'Cannot connect to Cloudflare services',
        suggestion: 'Check your internet connection, firewall, or VPN settings'
      };
    }
  }

  /**
   * 检查时间同步
   */
  static async checkTimeSync(): Promise<DiagnosticResult> {
    try {
      const response = await fetch('https://worldtimeapi.org/api/timezone/UTC');
      const data = await response.json();
      const serverTime = new Date(data.datetime);
      const localTime = new Date();
      const timeDiff = Math.abs(serverTime.getTime() - localTime.getTime());

      if (timeDiff > 300000) { // 5 minutes
        return {
          category: 'Time Synchronization',
          status: 'fail',
          message: `System clock is off by ${Math.round(timeDiff / 1000)} seconds`,
          suggestion: 'Sync your system clock with internet time servers'
        };
      }

      if (timeDiff > 60000) { // 1 minute
        return {
          category: 'Time Synchronization',
          status: 'warning',
          message: `System clock is slightly off by ${Math.round(timeDiff / 1000)} seconds`,
          suggestion: 'Consider syncing your system clock'
        };
      }

      return {
        category: 'Time Synchronization',
        status: 'pass',
        message: 'System clock is synchronized'
      };
    } catch (error) {
      return {
        category: 'Time Synchronization',
        status: 'warning',
        message: 'Could not verify time synchronization',
        suggestion: 'Ensure your system clock is correct'
      };
    }
  }

  /**
   * 检查第三方脚本加载
   */
  static async checkThirdPartyScripts(): Promise<DiagnosticResult> {
    try {
      // 检查是否能加载 Turnstile 脚本
      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      
      return new Promise((resolve) => {
        script.onload = () => {
          document.head.removeChild(script);
          resolve({
            category: 'Third-party Scripts',
            status: 'pass',
            message: 'Cloudflare Turnstile script loads successfully'
          });
        };

        script.onerror = () => {
          document.head.removeChild(script);
          resolve({
            category: 'Third-party Scripts',
            status: 'fail',
            message: 'Cannot load Cloudflare Turnstile script',
            suggestion: 'Check if ad blockers or security extensions are blocking the script'
          });
        };

        document.head.appendChild(script);
        
        // 超时处理
        setTimeout(() => {
          if (script.parentNode) {
            document.head.removeChild(script);
            resolve({
              category: 'Third-party Scripts',
              status: 'warning',
              message: 'Cloudflare Turnstile script loading timeout',
              suggestion: 'Check your internet connection or try disabling browser extensions'
            });
          }
        }, 10000);
      });
    } catch (error) {
      return {
        category: 'Third-party Scripts',
        status: 'fail',
        message: 'Error testing script loading',
        suggestion: 'Check browser security settings and extensions'
      };
    }
  }

  /**
   * 生成建议
   */
  static generateRecommendations(diagnostics: DiagnosticResult[]): string[] {
    const recommendations: string[] = [];
    const failedTests = diagnostics.filter(d => d.status === 'fail');
    const warningTests = diagnostics.filter(d => d.status === 'warning');

    if (failedTests.length > 0) {
      recommendations.push('Critical issues found that may prevent CF verification:');
      failedTests.forEach(test => {
        if (test.suggestion) {
          recommendations.push(`• ${test.suggestion}`);
        }
      });
    }

    if (warningTests.length > 0) {
      recommendations.push('Potential issues that may affect CF verification:');
      warningTests.forEach(test => {
        if (test.suggestion) {
          recommendations.push(`• ${test.suggestion}`);
        }
      });
    }

    if (failedTests.length === 0 && warningTests.length === 0) {
      recommendations.push('✅ All diagnostic tests passed. Your environment should support CF verification.');
    }

    return recommendations;
  }

  /**
   * 辅助方法：检测浏览器
   */
  private static detectBrowser(userAgent: string): { name: string; version: string } {
    const browsers = [
      { name: 'Chrome', regex: /Chrome\/(\d+)/ },
      { name: 'Firefox', regex: /Firefox\/(\d+)/ },
      { name: 'Safari', regex: /Version\/(\d+).*Safari/ },
      { name: 'Edge', regex: /Edg\/(\d+)/ }
    ];

    for (const browser of browsers) {
      const match = userAgent.match(browser.regex);
      if (match) {
        return { name: browser.name, version: match[1] };
      }
    }

    return { name: 'Unknown', version: '0' };
  }

  /**
   * 辅助方法：测试 localStorage
   */
  private static testLocalStorage(): boolean {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 辅助方法：测试 sessionStorage
   */
  private static testSessionStorage(): boolean {
    try {
      const test = 'test';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 便捷函数：运行 CF 诊断并输出结果
 */
export async function runCFDiagnostics(): Promise<void> {
  console.group('🔍 CF Verification Diagnostics');
  
  try {
    const result = await CFDiagnostics.runDiagnostics();
    
    console.log('📊 Environment Information:');
    console.table(result.environment);
    
    console.log('🧪 Diagnostic Results:');
    result.diagnostics.forEach(test => {
      const icon = test.status === 'pass' ? '✅' : test.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${test.category}: ${test.message}`);
      if (test.suggestion) {
        console.log(`   💡 ${test.suggestion}`);
      }
    });
    
    console.log('📋 Recommendations:');
    result.recommendations.forEach(rec => {
      console.log(rec);
    });
    
  } catch (error) {
    console.error('❌ Failed to run diagnostics:', error);
  }
  
  console.groupEnd();
}

// 在开发环境下自动添加到 window 对象，方便调试
if (import.meta.env.DEV) {
  (window as any).runCFDiagnostics = runCFDiagnostics;
  (window as any).CFDiagnostics = CFDiagnostics;
}
