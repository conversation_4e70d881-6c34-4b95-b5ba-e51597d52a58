<template>
  <ZPopOverlay :show="showKycVerifyTip">
    <div class="content" :style="{ backgroundImage: `url(${kycBg})` }">
      <img :src="kycClose" class="kyc-close" @click="handleClose" v-if="showClose" />
      <div class="form">
        <div class="row">
          <ZFormField
            :labelFontSize="12"
            ref="firstNameRef"
            labelAlias="First name"
            :validateOnBlur="false"
            label="First Name"
            v-model="formData.first_name"
            placeholder="Please enter "
            :maxLength="50"
            :required="true"
            @input="(value) => handleInput('first_name', value)"
          ></ZFormField>
          <ZFormField
            :labelFontSize="12"
            :validateOnBlur="false"
            :required="false"
            label="Middle Name"
            v-model="formData.middle_name"
            placeholder="Please enter "
            :maxLength="50"
            @input="(value) => handleInput('middle_name', value)"
          ></ZFormField>
        </div>
        <ZFormField
          :labelFontSize="12"
          ref="lastNameRef"
          labelAlias="Last name"
          :validateOnBlur="false"
          label="Last Name"
          v-model="formData.last_name"
          placeholder="Please enter your last name"
          :maxLength="50"
          :required="true"
          @input="(value) => handleInput('last_name', value)"
        ></ZFormField>
        <div class="form-item">
          <label>
            <!-- <span class="required-mark">*</span> -->
            Date of Birth</label
          >
          <div class="dob-wrapper">
            <input
              v-model="formData.day"
              placeholder="Day"
              @input="handleInput('day', $event.target.value)"
              type="number"
            />
            <input
              v-model="formData.month"
              placeholder="Month"
              @input="handleInput('month', $event.target.value)"
              type="number"
            />
            <input
              v-model="formData.year"
              placeholder="Year"
              @input="handleInput('year', $event.target.value)"
              type="number"
            />
          </div>
          <p class="error">
            <span v-if="dateInputErrTip">{{ dateInputErrTip }}</span>
          </p>
        </div>
        <div class="row">
          <ZUploadPhoto
            :labelFontSize="12"
            class="photo-item"
            ref="idPhotoRef"
            label="Government-Issued ID"
            :required="true"
            @confirm="(fontSideUrl) => handleUpload(PHOTO_TYPE.UP_ID_PHOTO, fontSideUrl)"
            photoLabel="front side of your ID card"
          ></ZUploadPhoto>
          <ZUploadPhoto
            :labelFontSize="12"
            class="photo-item"
            v-if="isFullKyc || isStandingKyc"
            ref="holdingPhotoRef"
            label="Selfie Holding Valid ID"
            :required="true"
            @confirm="(fontSideUrl) => handleUpload(PHOTO_TYPE.UP_ID_PHOTO_HOLDING, fontSideUrl)"
            photoLabel="selfie holding valid ID"
          ></ZUploadPhoto>
        </div>

        <div class="footer">
          <ZButton class="withdraw-btn" @click="handleClose"> Skip </ZButton>
          <!-- Withdraw -->
          <GradientButton
            @click="handleSubmit"
            :disabled="false"
            class="submit-btn"
            background-gradient="linear-gradient(90deg, #FFBE55 20.59%, #FF552A 94.85%)"
          >
            <div class="kyc-bounce">
              <div class="text">Get ₱{{ kycBonusInfo.kyc_completed_reward }} Now!</div>
            </div>
            Submit</GradientButton
          >
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useKycMgrStore, KYC_TYPE } from "@/stores/kycMgr";
import { watch, computed, ref } from "vue";
import kycBg from "@/assets/images/popDialog/kyc-bg.png";
import kycClose from "@/assets/images/popDialog/kyc-close.png";
import ZUploadPhoto from "@/components/ZUploadPhoto/index.vue";
import { checkLegalDate, PHOTO_TYPE, initFullFormData } from "@/stores/kyc";
import { postKycSubmit } from "@/api/kyc";
import { showToast } from "vant";

const StatusStr = ["Login form", "Complete form", "Simple form", "Standing KYC"];

const autoPopMgrStore = useAutoPopMgrStore();
const kycMgrStore = useKycMgrStore();
const { showKycVerifyTip } = storeToRefs(autoPopMgrStore);
const { kycBonusInfo, kycStatus, kycType, rejectMsg, isFullKyc, isStandingKyc, isSimpleKyc } =
  storeToRefs(kycMgrStore);
const dateInputErrTip = ref("");

// 组件引用
const firstNameRef = ref();
const lastNameRef = ref();
const idPhotoRef = ref();
const holdingPhotoRef = ref();

const formData = ref({
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
  font_side_url: "", // 身份证
  holding_font_side_url: "", // 手持身份证
});

// 关闭按钮
const showClose = computed(() => {
  return true;
});

const getStatus = computed(() => {
  let ttype = 0;
  if (kycType.value === KYC_TYPE.FULL) ttype = 0;
  if (kycType.value === KYC_TYPE.SIMPLE) ttype = 2;
  if (kycType.value === KYC_TYPE.STANDING) ttype = 3;
  return StatusStr[ttype];
});

// 提现按钮
const showWithdrawBtn = computed(() => {
  // 简版，有可以不验证的提现次数
  if (isSimpleKyc.value) {
    return true;
  }
  return false;
});

const handleInput = (key, value) => {
  // 更新表单数据
  formData.value[key] = value;

  // 对日期字段进行实时验证
  if (["day", "month", "year"].includes(key)) {
    const errStr = checkLegalDate(key, value, formData.value);
    dateInputErrTip.value = errStr;
  }
};

const handleClose = () => {
  showKycVerifyTip.value = false;
};
const handleSubmit = async () => {
  // 验证表单是否填写完整
  if (!validateForm()) {
    return;
  }
  try {
    await postKycSubmit({
      ...initFullFormData,
      ...formData.value,
      status: getStatus.value,
      stage: 1,
    });
    showToast("Your information has been successfully submitted and is under review.");
    showKycVerifyTip.value = false;
  } catch (error) {
    console.error("KYC 提交失败:", error);
    showToast("Submission failed, please try again.");
  }
};

/**
 * 验证表单是否填写完整
 * 使用各组件内部的验证机制
 */
const validateForm = (): boolean => {
  let isValid = true;

  // 1. 验证 ZFormField 组件
  const formFieldRefs = [
    { ref: firstNameRef, name: "First Name" },
    { ref: lastNameRef, name: "Last Name" },
  ];

  for (const field of formFieldRefs) {
    if (field.ref.value && !field.ref.value.validate()) {
      console.warn(`${field.name} 验证失败`);
      isValid = false;
    }
  }

  // 2. 验证日期输入（day, month, year）
  // 使用 checkLegalDate 函数进行完整的日期验证
  const dateFields = ["day", "month", "year"];

  for (const field of dateFields) {
    const value = formData.value[field];

    // 检查字段是否为空
    if (!value || value.toString().trim() === "") {
      // dateInputErrTip.value = `Please fill in ${field.charAt(0).toUpperCase() + field.slice(1)}`;
      dateInputErrTip.value = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
      console.warn(`日期字段验证失败: ${field} 未填写`);
      isValid = false;
      break; // 只显示第一个错误
    }

    // 使用 checkLegalDate 函数验证每个字段
    const dateValidationResult = checkLegalDate(field, value, formData.value);
    if (dateValidationResult) {
      dateInputErrTip.value = dateValidationResult;
      console.warn(`日期验证失败 (${field}):`, dateValidationResult);
      isValid = false;
      break; // 只显示第一个错误
    }
  }

  // 3. 验证 ZUploadPhoto 组件
  const uploadRefs = [
    { ref: idPhotoRef, name: "Government-Issued ID" },
    { ref: holdingPhotoRef, name: "Selfie Holding Valid ID" },
  ];

  for (const upload of uploadRefs) {
    if (upload.ref.value && !upload.ref.value.validate()) {
      console.warn(`${upload.name} 验证失败`);
      isValid = false;
    }
  }

  // 4. 清除日期错误提示（如果验证通过）
  if (isValid) {
    dateInputErrTip.value = "";
    console.log("✅ 表单验证通过");
  }

  return isValid;
};
const handleUpload = (type: PHOTO_TYPE, fontSideUrl) => {
  if (type === PHOTO_TYPE.UP_ID_PHOTO) {
    formData.value.font_side_url = fontSideUrl;
  } else if (type === PHOTO_TYPE.UP_ID_PHOTO_HOLDING) {
    formData.value.holding_font_side_url = fontSideUrl;
  }
};
watch(
  () => showKycVerifyTip.value,
  (newVal) => {
    if (newVal) {
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  position: relative;
  z-index: 9;
  width: 327px;
  height: 597px;
  background-size: 100%;
  .kyc-close {
    position: absolute;
    top: 88px;
    right: -5px;
    cursor: pointer;
    width: 24px;
    height: 24px;
  }
  .footer {
    position: relative;
    display: flex;
    gap: 12px;
    .withdraw-btn {
      flex: 1;
      background-color: #fff0e1;
      color: #f51933;
    }
    .submit-btn {
      flex: 1;
    }
    .kyc-bounce {
      position: absolute;
      top: -25px;
      right: 0;
      width: 126px;
      height: 42px;
      background: url("@/assets/images/popDialog/kyc-bounce.png") no-repeat center center;
      background-size: 100% 100%;
      .text {
        text-align: center;
        font-size: 12px;
        height: 42px;
        line-height: 42px;
        padding-left: 6px;
        font-style: italic;
      }
    }
  }
  .row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    .photo-item {
      width: 50% !important;
    }
  }
  .form {
    padding-top: 148px;
    padding-left: 16px;
    padding-right: 16px;
    .required-mark {
      color: #ff4757;
      margin-left: 2px;
    }
    .form-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      width: 100%;

      .error {
        color: #ff4757;
        font-size: 12px;
        margin-top: 2px;
        height: 10px;
        line-height: 1.4;
      }

      label {
        margin-bottom: 8px;
        color: #666;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      input {
        --van-field-border: none;
        /* 移除 Vant Field 默认底部边框 */
        --van-field-padding: 8px 0;
        /* 调整内边距，适配独占一行 */
        background-color: #f7f8fa;
        /* 背景色示例，可根据设计调整 */
        border-radius: 999px;
        display: inline-flex;
        height: 42px;
        box-sizing: border-box;
        padding: 12px 20px;
        align-items: center;
        flex-shrink: 0;
        color: #222;
        font-family: D-DIN;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.3px;

        &::placeholder {
          color: #c0c0c0;

          /* 输入框内默认字体 */
          font-family: Inter;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      .dob-wrapper {
        width: 100%;
        display: flex;
        gap: 10px;

        input {
          flex: 1;
          width: 33%;
        }
      }
    }
  }

  .close {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
