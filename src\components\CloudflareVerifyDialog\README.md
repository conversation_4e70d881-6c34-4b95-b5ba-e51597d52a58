# CloudflareVerifyDialog 组件

一个基于 Cloudflare Turnstile 的验证弹窗组件，提供简洁易用的验证界面。

## 特性

- 🚀 简单易用的弹窗验证界面
- 🎨 美观的 UI 设计，支持主题切换
- 🔧 灵活的配置选项
- 📱 响应式设计，支持移动端
- ⚡ 自动加载 Turnstile 脚本
- 🛡️ 完整的错误处理和重试机制
- 🎯 TypeScript 支持

## 基本用法

### 1. 组件方式使用

```vue
<template>
  <div>
    <van-button @click="showVerify = true">显示验证</van-button>

    <CloudflareVerifyDialog
      v-model="showVerify"
      :cf-type="CF_TURNSTILE_TYPE.LOGIN_SUBMIT"
      title="登录验证"
      description="请完成安全验证以继续登录"
      @success="handleSuccess"
      @error="handleError"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { CF_TURNSTILE_TYPE } from "@/utils/CloudflareMgr";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

const showVerify = ref(false);

const handleSuccess = (result) => {
  console.log("验证成功:", result);
  // 处理验证成功逻辑
};

const handleError = (error) => {
  console.error("验证失败:", error);
  // 处理验证失败逻辑
};

const handleCancel = () => {
  console.log("用户取消验证");
  // 处理取消逻辑
};
</script>
```

### 2. API 方式使用

```typescript
import { showCloudflareVerify, CF_TURNSTILE_TYPE } from "@/utils/CloudflareVerifyAPI";

// 基本用法
const result = await showCloudflareVerify({
  cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
  title: "登录验证",
  description: "请完成安全验证以继续登录",
});

if (result.success) {
  console.log("验证成功，Token:", result.token);
} else {
  console.log("验证失败或取消");
}
```

### 3. 快捷方法

```typescript
import {
  verifyLogin,
  verifyRegister,
  verifyKYC,
  verifyWithdrawal,
} from "@/utils/CloudflareVerifyAPI";

// 登录验证
const loginResult = await verifyLogin();

// 注册验证
const registerResult = await verifyRegister();

// KYC验证
const kycResult = await verifyKYC();

// 提款验证
const withdrawalResult = await verifyWithdrawal();
```

## Props

| 参数             | 类型              | 默认值                  | 说明                                   |
| ---------------- | ----------------- | ----------------------- | -------------------------------------- |
| modelValue       | boolean           | false                   | 控制弹窗显示/隐藏                      |
| title            | string            | 'Security Verification' | 弹窗标题                               |
| description      | string            | 'Please complete...'    | 描述文本                               |
| cfType           | CF_TURNSTILE_TYPE | 必填                    | 验证类型                               |
| showCancelButton | boolean           | true                    | 是否显示取消按钮                       |
| siteKey          | string            | 自动获取                | 自定义 Site Key                        |
| theme            | string            | 'light'                 | 主题 (light/dark/auto)                 |
| size             | string            | 'normal'                | 尺寸 (normal/compact)                  |
| appearance       | string            | 'always'                | 外观 (always/execute/interaction-only) |
| autoCloseDelay   | number            | 2000                    | 验证成功后自动关闭延迟时间(ms)         |

## Events

| 事件名            | 参数            | 说明                |
| ----------------- | --------------- | ------------------- |
| update:modelValue | boolean         | 更新 v-model 绑定值 |
| success           | TurnstileResult | 验证成功事件        |
| error             | string          | 验证失败事件        |
| cancel            | -               | 取消事件            |

## 验证类型 (CF_TURNSTILE_TYPE)

```typescript
enum CF_TURNSTILE_TYPE {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
  /** 注册提交 */
  REGISTER_SUBMIT = "SCENE_REGISTER",
}
```

## 验证模式配置 (SITE_KEY_MAP)

系统现在支持基于场景的智能验证模式选择，每个验证类型都有对应的配置：

```typescript
export const SITE_KEY_MAP = {
  // 本地开发环境
  LOCAL: {
    mode: "managed",
    sitekey: "1x00000000000000000000AA",
  },
  // 登录场景 - 无感校验
  SCENE_LOGIN: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 获取验证码场景 - 有感校验
  SCENE_GET_CODE: {
    mode: "managed",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // ... 更多场景配置
};
```

### 验证模式说明

- **有感校验 (managed)**: 用户需要主动交互完成验证，适用于重要操作
- **无感校验 (invisible)**: 在后台自动执行，用户无感知，适用于频繁操作

### 验证场景配置映射

| 验证类型                | 场景描述             | 验证模式  | 使用场景 |
| ----------------------- | -------------------- | --------- | -------- |
| `LOGIN_SUBMIT`          | 登录提交             | invisible | 无感校验 |
| `LOGIN_PHONE_GET_CODE`  | 获取验证码           | managed   | 有感校验 |
| `FORGET_PW_GET_CODE`    | 忘记密码获取验证码   | invisible | 无感校验 |
| `FORGET_PW_SUBMIT`      | 忘记密码提交         | invisible | 无感校验 |
| `WITHDRAWAL_SUBMIT`     | 提现提交             | invisible | 无感校验 |
| `KYC_SUBMIT`            | KYC 提交             | invisible | 无感校验 |
| `REGISTER_SUBMIT`       | 注册提交             | invisible | 无感校验 |
| `MODIFY_PHONE_GET_CODE` | 修改手机号获取验证码 | invisible | 无感校验 |

> **注意**: 验证模式配置可能会根据业务需求进行调整，具体配置请参考 `SITE_KEY_MAP` 中的最新设置。

## 返回结果

```typescript
interface VerifyResult {
  success: boolean; // 是否成功
  token?: string; // 验证 Token
  cfType: string; // 验证类型
  error?: string; // 错误信息
  cancelled?: boolean; // 是否被取消
}
```

## 高级用法

### 智能验证模式选择

系统现在支持根据验证类型自动选择最适合的验证模式：

```typescript
import { CloudflareMgr, CF_TURNSTILE_TYPE } from "@/utils/CloudflareMgr";

const mgr = CloudflareMgr.instance;

// 获取验证配置（包含 siteKey 和 mode）
const config = mgr.getSiteKey(CF_TURNSTILE_TYPE.LOGIN_SUBMIT);
console.log(config); // { siteKey: "0x4AAA...", mode: "invisible" }

// 判断是否为无感校验
const isInvisible = mgr.isInvisibleVerification(CF_TURNSTILE_TYPE.LOGIN_PHONE_GET_CODE);
console.log(isInvisible); // true

// 获取验证模式
const mode = mgr.getVerificationMode(CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT);
console.log(mode); // "invisible" 或 "managed"
```

### 根据验证模式动态处理

```typescript
function handleVerification(cfType: CF_TURNSTILE_TYPE) {
  const mgr = CloudflareMgr.instance;
  const isInvisible = mgr.isInvisibleVerification(cfType);

  if (isInvisible) {
    // 无感校验：自动在后台执行
    console.log("执行无感校验流程");
    executeInvisibleVerification(cfType);
  } else {
    // 有感校验：显示验证界面
    console.log("执行有感校验流程");
    showVerificationDialog(cfType);
  }
}
```

### 自定义配置

```typescript
const result = await showCloudflareVerify({
  cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
  title: "自定义验证",
  description: "这是一个自定义的验证弹窗",
  theme: "dark",
  size: "compact",
  appearance: "interaction-only",
  autoCloseDelay: 3000,
  showCancelButton: false,
});
```

### 错误处理

```typescript
try {
  const result = await verifyLogin();

  if (result.success) {
    // 验证成功
    console.log("Token:", result.token);
  } else if (result.cancelled) {
    // 用户取消
    console.log("用户取消了验证");
  } else {
    // 验证失败
    console.error("验证失败:", result.error);
  }
} catch (error) {
  // 系统错误
  console.error("系统错误:", error);
}
```

## 注意事项

1. **智能配置**: 系统现在优先使用 `SITE_KEY_MAP` 中的场景配置，自动选择最适合的验证模式
2. **环境配置**: 当场景配置不存在时，会回退到 `.env` 文件中的环境变量配置
3. **网络要求**: 需要能够访问 Cloudflare 的验证服务
4. **域名配置**: Site Key 需要与当前域名匹配
5. **脚本加载**: 组件会自动加载 Turnstile 脚本，无需手动引入

## 配置优先级

系统按以下优先级获取 Site Key 配置：

1. **场景配置优先**: 如果提供了 `CF_TURNSTILE_TYPE`，优先从 `SITE_KEY_MAP` 获取对应配置
2. **环境变量回退**: 如果场景配置不存在，回退到环境变量配置
3. **默认配置**: 最后使用系统默认配置

### 环境变量配置

```bash
# .env.development
VITE_CF_SITE_KEY_LOCAL=1x00000000000000000000AA  # 本地开发专用
VITE_CF_SITE_KEY_DEV=0x4AAAAAABnByxp2v1NuTm7f
VITE_CF_SITE_KEY_TEST=0x4AAAAAABnByxp2v1NuTm7f
VITE_CF_SITE_KEY_PRE=0x4AAAAAABpKiQV8_G7FJy6p
VITE_CF_SITE_KEY_PROD=0x4AAAAAABpKiQV8_G7FJy6p
```

### 场景配置示例

```typescript
// 在 CloudflareMgr.ts 中的 SITE_KEY_MAP
export const SITE_KEY_MAP = {
  SCENE_LOGIN: {
    mode: "invisible", // 无感校验
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  SCENE_GET_CODE: {
    mode: "managed", // 有感校验
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // ... 更多场景配置
};
```

## 故障排除

### 常见问题

1. **验证失败**: 检查 Site Key 是否正确配置
2. **脚本加载失败**: 检查网络连接和防火墙设置
3. **域名不匹配**: 确保 Site Key 配置了正确的域名
4. **样式问题**: 检查 CSS 是否正确加载
5. **场景配置缺失**: 检查 `SITE_KEY_MAP` 中是否包含对应的验证类型配置
6. **验证模式不符合预期**: 确认 `SITE_KEY_MAP` 中的 `mode` 配置是否正确

### 调试模式

在开发环境中，组件会输出详细的调试信息到控制台，帮助排查问题：

```javascript
// 在浏览器控制台中运行以下命令进行调试

// 1. 测试 Site Key 配置
const mgr = CloudflareMgr.instance;
mgr.testLocalSiteKeyConfig();

// 2. 测试 Site Key 验证
mgr.testSiteKeyValidation();

// 3. 检查特定场景的配置
const config = mgr.getSiteKey(CF_TURNSTILE_TYPE.LOGIN_SUBMIT);
console.log("登录场景配置:", config);

// 4. 检查验证模式
const isInvisible = mgr.isInvisibleVerification(CF_TURNSTILE_TYPE.LOGIN_PHONE_GET_CODE);
console.log("是否为无感校验:", isInvisible);
```

### 日志输出示例

系统会输出详细的日志信息：

```text
🔑 Using site key from SITE_KEY_MAP for scene: SCENE_LOGIN
📋 Mode: invisible (无感校验)
✅ Turnstile script loaded successfully
✅ Turnstile widget rendered successfully, ID: widget_123
```

## 新增组件

### TurnstileWidget.vue - 可复用的 Turnstile 组件

一个独立的、可复用的 Turnstile 验证组件，可以直接嵌入到任何表单中。

#### TurnstileWidget 属性

- `siteKey?: string` - Turnstile site key (可选，未提供时使用默认配置)
- `theme?: 'light' | 'dark' | 'auto'` - 主题 (默认: 'auto')
- `size?: 'normal' | 'compact'` - 尺寸 (默认: 'normal')
- `appearance?: 'always' | 'execute' | 'interaction-only'` - 显示模式 (默认: 'always')
- `language?: string` - 语言代码 (默认: 'en')
- `cfType?: CF_TURNSTILE_TYPE` - 验证类型 (默认: NONE)

#### TurnstileWidget 事件

- `@success="(token: string) => void"` - 验证成功
- `@error="(error: string) => void"` - 验证失败
- `@expired="() => void"` - 验证过期
- `@reset="() => void"` - 组件重置

#### TurnstileWidget 方法 (通过 ref 调用)

- `reset()` - 重置组件
- `retry()` - 重试验证

#### TurnstileWidget 使用示例

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <input v-model="email" type="email" required />

    <TurnstileWidget ref="turnstileRef" theme="light" @success="onSuccess" @error="onError" />

    <button type="submit" :disabled="!isVerified">提交</button>
  </form>
</template>

<script setup>
import { ref } from "vue";
import TurnstileWidget from "./TurnstileWidget.vue";

const email = ref("");
const isVerified = ref(false);
const verificationToken = ref("");
const turnstileRef = ref();

const onSuccess = (token) => {
  isVerified.value = true;
  verificationToken.value = token;
};

const onError = (error) => {
  isVerified.value = false;
  console.error("验证失败:", error);
};

const handleSubmit = async () => {
  if (!isVerified.value) return;

  // 提交表单，包含验证 token
  await submitForm({
    email: email.value,
    turnstileToken: verificationToken.value,
  });

  // 重置表单
  email.value = "";
  turnstileRef.value?.reset();
};
</script>
```

### 测试文件

#### test.vue

完整的测试页面，包含：

- 表单集成示例
- 配置选项测试
- 状态监控
- 错误处理演示

#### TurnstileExample.vue

简单的登录表单示例，展示基本集成方法。

### 测试用 Site Keys

- `1x00000000000000000000AA` - 总是通过验证
- `2x00000000000000000000AB` - 总是失败
- `3x00000000000000000000FF` - 总是显示交互式挑战

### 使用建议

1. **简单表单验证**: 使用 `TurnstileWidget.vue`
2. **弹窗验证**: 使用 `CloudflareVerifyDialog/index.vue`
3. **API 调用**: 使用 `CloudflareVerifyAPI.ts` 中的方法

选择合适的组件可以让集成更加简单和灵活。

## 最新更新 (v2.0)

### 🚀 智能验证模式

- **场景配置**: 新增 `SITE_KEY_MAP` 配置，支持基于验证类型的智能配置
- **验证模式**: 自动区分有感校验和无感校验，提供最佳用户体验
- **配置优先级**: 场景配置优先，环境变量回退，确保灵活性和兼容性

### 🔧 新增 API

```typescript
// 获取验证配置（包含 siteKey 和 mode）
const config = mgr.getSiteKey(CF_TURNSTILE_TYPE.LOGIN_SUBMIT);

// 判断是否为无感校验
const isInvisible = mgr.isInvisibleVerification(cfType);

// 获取验证模式
const mode = mgr.getVerificationMode(cfType);
```

### 📋 验证类型完善

- 支持所有业务场景的验证类型
- 每个场景都有对应的验证模式配置
- 详细的场景说明和使用建议

### 🛠️ 调试增强

- 详细的日志输出
- 完善的调试方法
- 配置验证和错误提示

### 🔄 向后兼容

- 保持所有现有 API 的兼容性
- 渐进式升级，无需修改现有代码
- 自动回退机制确保稳定性

---

**更新时间**: 2024-08-26
**版本**: v2.0
**兼容性**: 完全向后兼容
